#include "usart_app.h"
uint8_t uart_dma_buffer1[128];
struct rt_ringbuffer uart_ringbuffer1;
uint8_t ringbuffer_pool1[128];


int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// ��ʼ���ɱ�����б�
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if(huart->Instance==USART1)
    {
        HAL_UART_DMAStop(huart);
        rt_ringbuffer_put(&uart_ringbuffer1,uart_rx_dma_buffer1,Size);
             my_printf(&huart1,"%s\r\n",uart_rx_dma_buffer1);
        memset(uart_rx_dma_buffer1, 0, sizeof(uart_rx_dma_buffer1));
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer1, sizeof(uart_rx_dma_buffer1));
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx ,DMA_IT_HT);
    }
    
}

void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer1);

	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer1, uart_dma_buffer1, length);


	// ��ս��ջ�����
	memset(uart_dma_buffer1, 0, sizeof(uart_dma_buffer1));
}