--cpu=Cortex-M4.fp.sp
"presure\startup_stm32f429xx.o"
"presure\main.o"
"presure\gpio.o"
"presure\usart.o"
"presure\stm32f4xx_it.o"
"presure\stm32f4xx_hal_msp.o"
"presure\stm32f4xx_hal_uart.o"
"presure\stm32f4xx_hal_rcc.o"
"presure\stm32f4xx_hal_rcc_ex.o"
"presure\stm32f4xx_hal_flash.o"
"presure\stm32f4xx_hal_flash_ex.o"
"presure\stm32f4xx_hal_flash_ramfunc.o"
"presure\stm32f4xx_hal_gpio.o"
"presure\stm32f4xx_hal_dma_ex.o"
"presure\stm32f4xx_hal_dma.o"
"presure\stm32f4xx_hal_pwr.o"
"presure\stm32f4xx_hal_pwr_ex.o"
"presure\stm32f4xx_hal_cortex.o"
"presure\stm32f4xx_hal.o"
"presure\stm32f4xx_hal_exti.o"
"presure\system_stm32f4xx.o"
"presure\schedule.o"
"presure\led_app.o"
"presure\key_app.o"
"presure\usart_app.o"
"presure\ringbuffer.o"
--strict --scatter "presure\presure.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "presure.map" -o presure\presure.axf